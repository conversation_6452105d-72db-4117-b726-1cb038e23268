import type React from "react"
import { Inter, Ubuntu, Ralew<PERSON> } from "next/font/google"
import "./globals.css"
import Header from "@/components/header"
import Footer from "@/components/footer"
import ClientLayout from "@/components/client-layout"
import PageWrapper from "@/components/page-wrapper"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
})

const ubuntu = Ubuntu({
  weight: ["700"],
  subsets: ["latin"],
  variable: "--font-ubuntu",
  display: "swap",
})

const raleway = Raleway({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-raleway",
  display: "swap",
})

export const metadata = {
  title: "DRILL - User Research Lab",
  description: "Accelerate your research and get 10X accurate results with DRILL",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body 
        className={`${inter.className} font-sans ${ubuntu.variable} ${raleway.variable}`}
        style={{ paddingTop: '20px' }}
      >
        <ClientLayout>
          <div className="w-full mx-auto">
            <PageWrapper>
              <Header />
              <main>{children}</main>
              <Footer />
            </PageWrapper>
          </div>
        </ClientLayout>
      </body>
    </html>
  )
}
