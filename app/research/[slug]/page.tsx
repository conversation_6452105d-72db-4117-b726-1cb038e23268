import { PortableText, PortableTextComponents } from '@portabletext/react'
import { TypedObject } from '@portabletext/types'
import type { BlogPost } from "@/types/blog"
import { client } from "@/lib/sanity.client"
import { urlForImage } from "@/lib/sanity.image"
import { notFound } from "next/navigation"
import Image from "next/image"
import Link from "next/link"

const POST_QUERY = `*[_type == "post" && slug.current == $slug][0]{
  _id,
  title,
  description,
  mainImage,
  publishedAt,
  body,
  author->{
    name,
    image
  },
  "readTime": round(length(pt::text(body)) / 5 / 180)
}`

export async function generateStaticParams() {
  const slugs = await client.fetch<string[]>(
    `*[_type == "post" && defined(slug.current)].slug.current`
  )
  return slugs.map((slug) => ({ slug }))
}

// Define components for PortableText
const components: PortableTextComponents = {
  types: {
    image: ({ value }: any) => {
      if (!value?.asset?._ref) {
        return null
      }
      return (
        <div className="my-6 md:my-8">
          <div className="relative w-full max-w-[996px] h-[250px] md:h-[410px] mx-auto rounded-[20px] md:rounded-[40px] overflow-hidden">
            <Image
              className="object-cover"
              src={urlForImage(value)}
              alt={value.alt || ' '}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
            />
          </div>
        </div>
      )
    }
  },
  block: {
    h1: ({ children }) => (
      <h1 className="text-xl md:text-[24px] font-bold text-black mt-6 md:mt-8 mb-3 md:mb-4 leading-tight max-w-5xl mx-auto">
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-lg md:text-[22px] font-bold text-black mt-6 md:mt-8 mb-3 md:mb-4 leading-tight max-w-5xl mx-auto">
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-base md:text-[20px] font-bold text-black mt-4 md:mt-6 mb-2 md:mb-3 leading-tight max-w-5xl mx-auto">
        {children}
      </h3>
    ),
    normal: ({ children }) => (
      <p className="text-sm md:text-[16px] leading-relaxed text-[#666666] mb-3 md:mb-4 max-w-5xl mx-auto">
        {children}
      </p>
    ),
  },
  list: {
    bullet: ({ children }) => (
      <ul className="list-none mb-4 md:mb-6 space-y-2 max-w-5xl mx-auto">
        {children}
      </ul>
    ),
    number: ({ children }) => (
      <ol className="list-none mb-4 md:mb-6 space-y-2 max-w-5xl mx-auto">
        {children}
      </ol>
    ),
  },
  listItem: {
    bullet: ({ children }) => (
      <li className="text-sm md:text-[16px] text-[#666666] leading-relaxed flex items-start">
        <span className="text-black mr-3">•</span>
        <span className="flex-1">{children}</span>
      </li>
    ),
    number: ({ children, index }) => (
      <li className="text-sm md:text-[16px] text-[#666666] leading-relaxed flex items-start">
        <span className="text-black mr-3 font-semibold">{(index || 0) + 1}.</span>
        <span className="flex-1">{children}</span>
      </li>
    ),
  },
}

export default async function BlogPost({
  params,
}: {
  params: Promise<{ slug: string }>
}) {
  const { slug } = await params
  const post = await client.fetch<BlogPost>(
    POST_QUERY,
    { slug },
    { next: { revalidate: 30 } }
  )
  
  if (!post || !post.body) {
    notFound()
  }

  return (
    <div className="min-h-screen relative">
      <div className="container mx-auto px-6 md:px-4 pt-24 md:pt-32 pb-16">
        <article className="max-w-6xl mx-auto">
          {/* Header Section */}
          <header className="text-center mb-8 md:mb-12">
            <h1 className="text-2xl md:text-[48px] font-bold text-black leading-[1.2] mb-4 md:mb-6 max-w-4xl mx-auto">
              {(() => {
                const words = post.title.split(' ');
                const midpoint = Math.ceil(words.length / 2);
                const firstLine = words.slice(0, midpoint);
                const secondLine = words.slice(midpoint);
                
                return (
                  <>
                    <div>
                      {firstLine.map((word, index) => 
                        word.toLowerCase() === 'research' ? (
                          <span key={index} className="text-[#BC77FF]">{word} </span>
                        ) : (
                          <span key={index}>{word} </span>
                        )
                      )}
                    </div>
                    {secondLine.length > 0 && (
                      <div>
                        {secondLine.map((word, index) => 
                          word.toLowerCase() === 'research' ? (
                            <span key={index + midpoint} className="text-[#BC77FF]">{word} </span>
                          ) : (
                            <span key={index + midpoint}>{word} </span>
                          )
                        )}
                      </div>
                    )}
                  </>
                );
              })()}
            </h1>
            
            {/* Author info */}
            <div className="flex items-center justify-center gap-2 md:gap-3 mb-8 md:mb-12">
              {post.author?.image && (
                <Image
                  src={urlForImage(post.author.image)}
                  alt={post.author.name}
                  width={20}
                  height={20}
                  className="rounded-full"
                />
              )}
              <span className="text-[#666666] text-xs md:text-[14px]">
                {post.author?.name || 'Anonymous'}
              </span>
              <span className="text-[#666666]">|</span>
              <span className="text-[#666666] text-xs md:text-[14px]">{post.readTime || 10} min read</span>
              <Image
                src="/send.svg"
                alt="Reading time"
                width={20}
                height={20}
                className="ml-1 w-5 h-5 md:w-6 md:h-6"
              />
            </div>
          </header>

          {/* Featured Image */}
          {post.mainImage && (
            <div className="mb-8 md:mb-12">
              <div className="relative w-full max-w-[1200px] h-[250px] md:h-[440px] mx-auto rounded-[20px] md:rounded-[40px] overflow-hidden">
                <Image
                  src={urlForImage(post.mainImage)}
                  alt={post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                  priority
                />
              </div>
            </div>
          )}

          {/* Content */}
          <div className="max-w-none px-4 md:px-0">
            <PortableText 
              value={post.body}
              components={components}
            />
          </div>
        </article>
      </div>
    </div>
  )
}