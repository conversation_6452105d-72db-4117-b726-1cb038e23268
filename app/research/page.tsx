"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { client } from "@/lib/sanity.client"
import { urlForImage } from "@/lib/sanity.image"

// Update the POSTS_QUERY to include category
const POSTS_QUERY = `*[_type == "post" && defined(slug.current)] {
  _id,
  title,
  slug,
  publishedAt,
  mainImage,
  description,
  category,
  author->{
    name,
    image
  }
}`

interface BlogPost {
  _id: string
  title: string
  slug: { current: string }
  mainImage: any
  publishedAt: string
  description: string
  category: string
  author: {
    name: string
    image: any
  }
}

const filters = ["All", "User Research", "User Centered Design", "UI Design", "UX Design", "Design Thinking"]

export default function ResearchPage() {
  const [activeFilter, setActiveFilter] = useState("All")
  const [hoveredArticle, setHoveredArticle] = useState<number | null>(null)
  const [blogs, setBlogs] = useState<BlogPost[]>([])
  const [filteredBlogs, setFilteredBlogs] = useState<BlogPost[]>([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  useEffect(() => {
    loadBlogs()
  }, [])

  // Add new useEffect to handle filtering
  useEffect(() => {
    filterBlogs()
  }, [activeFilter, blogs])

  const loadBlogs = async (resetPage: boolean = true) => {
    try {
      setLoading(true)
      const currentPage = resetPage ? 1 : page
      const pageSize = 9
      
      const posts = await client.fetch(POSTS_QUERY, {}, { next: { revalidate: 30 } })
      const paginatedPosts = posts.slice(0, currentPage * pageSize)
      
      setBlogs(resetPage ? paginatedPosts : [...blogs, ...paginatedPosts])
      setHasMore(paginatedPosts.length < posts.length)
      setPage(currentPage + 1)
    } catch (error) {
      console.error('Error loading blogs:', error)
    } finally {
      setLoading(false)
    }
  }

  // Add new function to handle filtering
  const filterBlogs = () => {
    if (activeFilter === "All") {
      setFilteredBlogs(blogs)
      return
    }

    const filtered = blogs.filter(blog => {
      // Convert the category value to match the filter text
      const categoryValue = blog.category.replace(/-/g, ' ')
        .toLowerCase()
      return categoryValue === activeFilter.toLowerCase()
    })

    setFilteredBlogs(filtered)
    setHasMore(filtered.length >= page * 9)
  }

  // Update the filter click handler
  const handleFilterClick = (filter: string) => {
    setActiveFilter(filter)
    setPage(1)
  }

  return (
    <div className="min-h-screen">
      {/* Decorative Elements */}
      <div className="hidden md:block absolute top-12 left-1/4 w-8 h-8 bg-purple-500 rounded-full opacity-20" />
      <div className="hidden md:block absolute top-16 right-1/4 w-6 h-6 bg-purple-500 rounded-full opacity-20" />

      <div className="container mx-auto px-6 md:px-4 pt-24 md:pt-[100px] pb-16">
        <div className="text-center mb-16 md:mb-[140px]">
          <h1 className="font-ubuntu text-2xl md:text-[48px] font-bold mb-2">
            Research & <span className="text-[#BC77FF]">Innovation</span>
          </h1>
          <p className="font-ubuntu text-base md:text-[20px] font-bold text-[#717171]">
            Explore insights from our experts
          </p>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap justify-center gap-2 md:gap-4 mb-8 md:mb-16 px-4">
          {filters.map((filter) => (
            <button
              key={filter}
              onClick={() => handleFilterClick(filter)}
              className={`px-3 md:px-6 py-2 rounded-full transition-all border text-sm md:text-base ${
                activeFilter === filter 
                ? "bg-black text-white border-black" 
                : "bg-white text-gray-600 hover:bg-gray-50 border-[#E5E7EB]"
              }`}
            >
              {filter}
            </button>
          ))}
        </div>

        {/* Blog Grid */}
        <div className="max-w-[1300px] mx-auto mb-16">
          {/* First row of blogs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[12px] mb-8 md:mb-[60px]">
            {filteredBlogs.slice(0, 3).map((blog, i) => (
              <BlogCard 
                key={blog._id} 
                blog={blog} 
                index={i} 
                isHovered={hoveredArticle === i}
                onHover={setHoveredArticle}
              />
            ))}
          </div>

          {/* Second row of blogs */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[12px]">
            {filteredBlogs.slice(3, 6).map((blog, i) => (
              <BlogCard 
                key={blog._id} 
                blog={blog} 
                index={i + 3} 
                isHovered={hoveredArticle === (i + 3)}
                onHover={setHoveredArticle}
              />
            ))}
          </div>
        </div>

        {/* Load More Button */}
        {hasMore && filteredBlogs.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={() => loadBlogs(false)}
              disabled={loading}
              className="btn-view-services bg-black text-white flex items-center gap-2 w-full max-w-[280px] h-12 md:w-auto md:h-auto justify-center"
            >
              {loading ? 'Loading...' : 'Load More'}
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vector-NruOIe9b7aoZD0GcrxrxwnX9Y0xqVK.svg"
                alt="Arrow"
                width={38}
                height={38}
                className="w-6 h-6 md:w-[38px] md:h-[38px]"
              />
            </button>
          </div>
        )}

        {/* Add No Results Message */}
        {filteredBlogs.length === 0 && !loading && (
          <div className="text-center text-gray-500 mt-8">
            No blog posts found for this category.
          </div>
        )}
      </div>
    </div>
  )
}

// Updated BlogCard component with new styling
type BlogCardProps = {
  blog: BlogPost
  index: number
  isHovered: boolean
  onHover: (index: number | null) => void
}

const BlogCard = ({ blog, index, isHovered, onHover }: BlogCardProps) => {
  const [localHovered, setLocalHovered] = useState(false)

  return (
    <Link href={`/research/${blog.slug.current}`}>
      <div
        className="relative w-full max-w-[387px] h-[300px] md:h-[440px] overflow-visible cursor-pointer group mx-auto"
        onMouseEnter={() => {
          setLocalHovered(true)
          onHover(index)
        }}
        onMouseLeave={() => {
          setLocalHovered(false)
          onHover(null)
        }}
      >
        {/* Skeleton background */}
        <div
          className="absolute inset-0 rounded-[40px] md:rounded-[60px] opacity-70"
          style={{
            backgroundImage: `url('/skeleton.svg')`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            zIndex: 1,
          }}
        />

        {/* Main card container with hover tilt */}
        <div
          className="absolute inset-0 transition-all duration-300"
          style={{
            transform: localHovered ? "rotate(-6.41deg)" : "rotate(0deg)",
            transformOrigin: "center center",
            zIndex: 2,
          }}
        >
          {/* Card content */}
          <div className="relative w-full h-full rounded-[40px] md:rounded-[60px] overflow-hidden">
            {/* Background image */}
            <Image 
              src={blog.mainImage && blog.mainImage.asset ? urlForImage(blog.mainImage) : "/placeholder.svg"} 
              alt={blog.title} 
              fill 
              className="object-cover"
            />

            {/* Gradient overlays */}
            <div
              className="absolute top-0 left-0 right-0 transition-opacity duration-300"
              style={{
                backgroundImage: `url('/grey gradient.svg')`,
                backgroundSize: "cover",
                height: "250px",
                opacity: localHovered ? 0 : 0.7,
                borderTopLeftRadius: "40px",
                borderTopRightRadius: "40px",
              }}
            />
            <div
              className="absolute top-0 left-0 right-0 transition-opacity duration-300 md:h-[325px] h-[250px]"
              style={{
                backgroundImage: `url('/purple gradient.svg')`,
                backgroundSize: "cover",
                opacity: localHovered ? 0.7 : 0,
                borderTopLeftRadius: "40px",
                borderTopRightRadius: "40px",
              }}
            />

            {/* Text content */}
            <div className="absolute top-4 md:top-8 left-4 md:left-8 z-20">
              <h3 className="text-white text-lg md:text-[28px] font-bold mb-2 md:mb-3 whitespace-pre-line max-w-[90%]">
                {blog.title}
              </h3>
              <div
                className={`max-w-[80%] text-white transition-all duration-300 text-sm md:text-base ${
                  localHovered ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
                }`}
              >
                <p>{blog.description}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  )
}