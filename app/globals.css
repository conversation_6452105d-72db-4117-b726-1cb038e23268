@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --navigation-menu-trigger-icon-size: 0;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html, body {
    margin: 0;
    padding: 0;
    background: transparent;
  }

  body {
    background: transparent;
  }
}

@layer components {
  .download-button {
    @apply flex items-center justify-center gap-2 bg-black text-white rounded-full px-8 py-4 hover:bg-gray-800 transition-colors;
    width: fit-content;
    margin: 0 auto;
  }

  .feature-card {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 387px;
    height: 528px;
    background-color: #F5F6FA;
    border-radius: 60px;
    padding: 2rem;
    overflow: hidden;
    transition: transform 0.2s ease;
    margin: 0 auto;
  }

  .feature-card-mobile {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    max-width: 387px;
    height: auto;
    min-height: 400px;
    background-color: #F5F6FA;
    border-radius: 40px;
    padding: 1.5rem;
    overflow: hidden;
    transition: transform 0.2s ease;
    margin: 0 auto;
  }

  @media (min-width: 768px) {
    .feature-card-mobile {
      width: 387px;
      height: 528px;
      border-radius: 60px;
      padding: 2rem;
      min-height: auto;
    }
  }

  .feature-card:hover,
  .feature-card-mobile:hover {
    transform: translateY(-5px);
  }

  .feature-card-content {
    position: relative;
    z-index: 2;
  }

  .feature-card-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    height: 240px;
    position: relative;
  }

  .feature-card-image-container-mobile {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1.5rem;
    height: 200px;
    position: relative;
  }

  @media (min-width: 768px) {
    .feature-card-image-container-mobile {
      margin-top: 2rem;
      height: 240px;
    }
  }

  .feature-card-image {
    position: relative;
    width: 240px;
    height: 240px;
    transition: all 0.5s ease;
    filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.2));
  }

  .feature-card-image.hovered {
    transform: scale(1.05);
    filter: drop-shadow(0 0 20px rgba(168, 85, 247, 0.5));
  }

  .feature-card-video {
    position: relative;
    margin: 0 auto;
    transition: all 0.2s ease;
    filter: none;
  }

  .feature-card-video-mobile {
    position: relative;
    margin: 0 auto;
    transition: all 0.2s ease;
    filter: none;
    width: 200px;
    height: 200px;
  }

  @media (min-width: 768px) {
    .feature-card-video-mobile {
      width: 257px;
      height: 305px;
    }
  }

  .feature-card-video.hovered,
  .feature-card-video-mobile.hovered {
    transform: scale(1.05);
    filter: none;
  }

  .feature-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

@layer utilities {
  .font-raleway {
    font-family: var(--font-raleway), sans-serif;
  }

  .font-ubuntu {
    font-family: var(--font-ubuntu), sans-serif;
  }

  /* Hero animation container */
  .hero-animation-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
    z-index: 0;
  }

  .hero-animation-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Button styles */
  .btn-get-in-touch {
    width: 185px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 50px !important; /* Space for the arrow */
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-get-in-touch::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-get-in-touch:hover::before {
    opacity: 1;
  }

  .btn-get-in-touch:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  /* Submit Button - Left aligned text */
  .btn-submit {
    width: 185px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding-right: 50px !important;
    padding-left: 25px !important;
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-submit::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-submit:hover::before {
    opacity: 1;
  }

  .btn-submit:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  .btn-experience {
    width: 267px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    position: relative;
    padding-right: 50px !important; /* Space for the arrow */
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-experience::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-experience:hover::before {
    opacity: 1;
  }

  .btn-experience:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  .btn-download {
    width: 305px;
    height: 50px;
    border-radius: 30.4px !important;
    @apply transition-all duration-300 border-none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
    background-color: #f2f2f2 !important;
    color: black !important;
  }

  .btn-download:hover {
    @apply shadow-lg shadow-purple-500/20;
    background-color: #f2f2f2 !important;
    color: black !important;
  }

  .btn-view-services {
    width: 233px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-right: 50px !important;
    margin: 0 auto;
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-view-services::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-view-services:hover::before {
    opacity: 1;
  }

  .btn-view-services:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  /* Purchase Now Button */
  .btn-purchase-now {
    width: 307px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-right: 100px !important;
    padding-left: 1px !important;
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-purchase-now::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-purchase-now:hover::before {
    opacity: 1;
  }

  .btn-purchase-now:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  /* Customize Now Button */
  .btn-customize-now {
    width: 217px;
    height: 50px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-right: 40px !important;
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 18px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-customize-now::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-customize-now:hover::before {
    opacity: 1;
  }

  .btn-customize-now:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  /* Enquire Now Button */
  .btn-enquire-now {
    width: 160px;
    height: 40px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-right: 45px !important;
    background-color: #f2f2f2 !important;
    color: black !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-enquire-now::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(188, 119, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-enquire-now:hover::before {
    opacity: 1;
  }

  .btn-enquire-now:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  /* Enquire Now Black Button for Large Organization */
  .btn-enquire-now-black {
    width: 148px;
    height: 40px;
    border-radius: 38px !important;
    @apply transition-all duration-300;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding-right: 30px !important;
    background-color: #000 !important;
    color: white !important;
    overflow: hidden;
    font-family: var(--font-ubuntu);
    font-weight: 700;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0%;
  }

  .btn-enquire-now-black::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .btn-enquire-now-black:hover::before {
    opacity: 1;
  }

  .btn-enquire-now-black:hover {
    @apply shadow-lg shadow-purple-500/20;
  }

  .btn-get-in-touch img,
  .btn-experience img,
  .btn-view-services img,
  .btn-purchase-now img,
  .btn-customize-now img,
  .btn-enquire-now img,
  .btn-enquire-now-black img,
  .btn-submit img {
    position: absolute;
    right: 6px;
    width: 38px;
    height: 38px;
    z-index: 1;
  }

  /* Smaller arrows for enquire now buttons */
  .btn-enquire-now img,
  .btn-enquire-now-black img {
    width: 30px;
    height: 30px;
  }

  /* Animation utilities */
  .animate-spin-slow {
    animation: spin 15s linear infinite;
  }

  .animate-spin-slow-reverse {
    animation: spin 12s linear infinite reverse;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Navigation menu styles */
[data-radix-navigation-menu-trigger] {
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

[data-radix-navigation-menu-trigger][data-state="open"] {
  background: transparent !important;
}

[data-radix-navigation-menu-trigger]::before,
[data-radix-navigation-menu-trigger]::after {
  display: none !important;
}

/* Arrow animation */
.arrow-icon {
  transition: transform 0.2s ease;
}

.arrow-icon[data-state="open"] {
  transform: rotate(180deg);
}

/* Dropdown styles */
.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  background: white;
  min-width: 220px;
  padding: 1rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  z-index: 50;
  margin-top: 0.5rem;
}

.dropdown-container:hover .dropdown-content {
  visibility: visible;
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.dropdown-container:hover .services-arrow {
  transform: rotate(180deg);
}

.services-arrow {
  transition: transform 0.2s ease-in-out;
}

.dropdown-content::before {
  content: "";
  position: absolute;
  top: -6px;
  left: 50%;
  transform: translateX(-50%) rotate(45deg);
  width: 12px;
  height: 12px;
  background: white;
}

/* Research card styles */
.research-card-skeleton {
  position: absolute;
  inset: -5%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.research-card-skeleton.visible {
  opacity: 1;
}

  /* Remove blue background from autofilled inputs */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #F5F6FA inset !important;
    -webkit-text-fill-color: #000000 !important;
    background-color: transparent !important;
    background-image: none !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  /* For Firefox */
  input:-moz-autofill,
  input:-moz-autofill:hover,
  input:-moz-autofill:focus {
    background-color: transparent !important;
    background-image: none !important;
  }

  /* Additional override for contact form inputs specifically */
  .contact-form input:-webkit-autofill,
  .contact-form input:-webkit-autofill:hover,
  .contact-form input:-webkit-autofill:focus,
  .contact-form input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px #F5F6FA inset !important;
    -webkit-text-fill-color: #000000 !important;
    background-color: #F5F6FA !important;
    transition: background-color 5000s ease-in-out 0s !important;
  }

  /* Media queries */
/* Mobile First - Extra Small Devices (320px and up) */
@media (max-width: 374px) {
  .hero-animation-container video {
    height: 100vh;
    width: 100vw;
    object-position: center center;
    object-fit: cover;
  }

  /* Extra small mobile typography */
  h1 {
    line-height: 1.1 !important;
    font-size: clamp(24px, 6vw, 28px) !important;
  }

  h2 {
    line-height: 1.2 !important;
    font-size: clamp(18px, 5vw, 24px) !important;
  }

  /* Container adjustments for very small screens */
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    max-width: 100%;
  }

  /* Hero section adjustments */
  .hero-animation-container {
    height: 100vh;
    width: 100vw;
  }
}

/* Small Mobile Devices (375px and up) */
@media (min-width: 375px) and (max-width: 413px) {
  .hero-animation-container video {
    height: 100vh;
    width: 100vw;
    object-position: center center;
    object-fit: cover;
  }

  h1 {
    line-height: 1.15 !important;
    font-size: clamp(26px, 6vw, 30px) !important;
  }

  h2 {
    line-height: 1.25 !important;
    font-size: clamp(20px, 5vw, 26px) !important;
  }
}

/* Large Mobile Devices (414px and up) */
@media (min-width: 414px) and (max-width: 767px) {
  .hero-animation-container video {
    height: 100vh;
    width: 100vw;
    object-position: center center;
    object-fit: cover;
  }

  h1 {
    line-height: 1.2 !important;
    font-size: clamp(28px, 6vw, 32px) !important;
  }

  h2 {
    line-height: 1.3 !important;
    font-size: clamp(22px, 5vw, 28px) !important;
  }
}

/* Tablet and Desktop (768px and up) - Preserve existing */
@media (max-width: 768px) {
  .hero-animation-container video {
    height: 100vh;
    width: 100vw;
    object-position: center center;
    object-fit: cover;
  }

  /* Mobile typography adjustments */
  h1 {
    line-height: 1.2 !important;
  }

  h2 {
    line-height: 1.3 !important;
  }

  /* Mobile button adjustments */
  .btn-get-in-touch,
  .btn-view-services,
  .btn-submit,
  .btn-experience,
  .btn-download,
  .btn-purchase-now,
  .btn-customize-now,
  .btn-enquire-now,
  .btn-enquire-now-black {
    width: 100%;
    max-width: 320px;
    height: 48px;
    font-size: 14px;
    padding: 0 20px;
    border-radius: 24px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn-get-in-touch img,
  .btn-experience img,
  .btn-view-services img,
  .btn-purchase-now img,
  .btn-customize-now img,
  .btn-enquire-now img,
  .btn-enquire-now-black img,
  .btn-submit img {
    width: 20px;
    height: 20px;
    right: 8px;
  }

  /* Mobile spacing adjustments */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
    max-width: 100%;
  }

  /* Mobile card adjustments */
  .research-card-skeleton {
    border-radius: 30px;
  }

  /* Mobile section spacing */
  section {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile hero content positioning */
  .hero-content {
    padding: 0 1rem;
    text-align: center;
  }

  /* Mobile mesh background adjustments */
  .background-mesh-mobile {
    width: 100vw !important;
    max-width: none !important;
    background-size: cover !important;
    background-position: center top !important;
  }
}

@media (max-width: 640px) {
  /* Extra small mobile adjustments */
  .feature-card-mobile {
    border-radius: 30px;
    padding: 1.25rem;
    min-height: 350px;
    margin: 0 auto;
    max-width: 100%;
  }

  .feature-card-video-mobile {
    width: 180px;
    height: 180px;
  }

  .feature-card-image-container-mobile {
    height: 180px;
    margin-top: 1rem;
  }

  /* Mobile hero adjustments */
  .hero-animation-container {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  .hero-animation-container video {
    width: 100vw;
    height: 100vh;
    object-fit: cover;
    object-position: center center;
  }

  /* Mobile text adjustments */
  .container {
    max-width: 100%;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* Mobile button text size */
  .btn-get-in-touch,
  .btn-view-services,
  .btn-submit,
  .btn-experience,
  .btn-download,
  .btn-purchase-now,
  .btn-customize-now,
  .btn-enquire-now,
  .btn-enquire-now-black {
    font-size: 14px !important;
    min-width: 140px;
    height: 40px;
    padding: 0 16px;
    max-width: 280px;
    margin: 0 auto;
  }

  /* Mobile spacing for sections */
  section {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  /* Mobile hero content spacing */
  .hero-content {
    padding: 0 0.75rem;
  }

  /* Mobile download button */
  .download-button {
    max-width: 280px;
    margin: 0 auto;
    padding: 0 1rem;
  }
}
