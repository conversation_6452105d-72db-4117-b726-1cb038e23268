"use client"

import { useState, useEffect, useRef } from "react"
import DownloadReportDialog from "@/components/download-report-dialog"
import ResearchGrid from "@/components/research-grid"
import ScrollAnimation from "@/components/scroll-animation"
import FeatureCard from "@/components/feature-card"

export default function Home() {
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false)
  const [activeSlide, setActiveSlide] = useState(0)
  const [featuresVisible, setFeaturesVisible] = useState(false)
  const [mobileFeatureIndex, setMobileFeatureIndex] = useState(0)
  const [isMobile, setIsMobile] = useState(false)
  const featuresSectionRef = useRef<HTMLDivElement>(null)

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Auto-slide functionality for hero
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveSlide((prev) => (prev + 1) % 3);
    }, 5000); // Change slide every 5 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Auto-slide functionality for mobile feature cards
  useEffect(() => {
    if (!isMobile) return
    
    const interval = setInterval(() => {
      setMobileFeatureIndex((prev) => (prev + 1) % features.length);
    }, 4000); // Change feature card every 4 seconds
    
    return () => clearInterval(interval);
  }, [isMobile]);

  // Track when features section is visible - optimized for performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        // Only set visible when section is actually in viewport
        if (entry.isIntersecting) {
          setFeaturesVisible(true);
          // Keep observer to handle leaving viewport for performance
        } else {
          setFeaturesVisible(false);
        }
      },
      {
        root: null,
        rootMargin: '100px 0px 0px 0px', // Reduced margin for better performance
        threshold: 0.1, // Increased threshold for better performance
      }
    );

    if (featuresSectionRef.current) {
      observer.observe(featuresSectionRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const features = [
    {
      title: "Save Your Time",
      description: "You can focus on other aspects of your business and leave the research part for DRILL.",
      icon: "/save-time-icon.svg",
    },
    {
      title: "Competitive Edge",
      description: "What did you hear about data? It brings you to the top of your game in the industry!",
      icon: "/lightbulb-icon.svg",
    },
    {
      title: "Innovation At A Whiff",
      description:
        "Our experts do not only research for business gain; they research for your brand to make an impact.",
      icon: "/target-icon.svg",
    },
  ]

  const handleFeatureNext = () => {
    setMobileFeatureIndex((prev) => (prev + 1) % features.length)
  }

  const handleFeaturePrev = () => {
    setMobileFeatureIndex((prev) => (prev - 1 + features.length) % features.length)
  }

  return (
    <div className="relative">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with animation - full viewport height */}
        <div className="fixed top-0 left-0 w-full h-screen z-0">
          <ScrollAnimation />
        </div>

        <div className="container mx-auto relative z-10 px-2 sm:px-4">
          <div className="flex flex-col items-center text-center mt-[-150px] sm:mt-[-200px] md:mt-[-300px]">
            <div className="max-w-4xl w-full px-2 sm:px-4">
              {/* Slide 1 - Welcome */}
              {activeSlide === 0 && (
                <>
                  <h1 className="font-ubuntu text-[24px] xs:text-[28px] sm:text-[32px] md:text-[48px] font-bold mb-6 md:mb-[38px] leading-tight tracking-tight px-2 sm:px-4" style={{ letterSpacing: '0.02em' }}>
                    <span className="block">Welcome To DRILL</span>
                    <span className="block mt-2">- Your UX Innovation</span>
                    <span className="text-white inline-flex items-center justify-center mt-4 w-[140px] h-[44px] xs:w-[160px] xs:h-[48px] sm:w-[180px] sm:h-[56px] md:w-[203px] md:h-[66px] text-[24px] xs:text-[28px] sm:text-[32px] md:text-[48px] font-ubuntu font-bold" style={{ borderRadius: '21px', background: 'linear-gradient(to bottom, #A03FFF, #CE9BFF)' }}>Partner</span>
                  </h1>
                  <p className="font-ubuntu text-sm sm:text-base md:text-[20px] mb-8 md:mb-[54px] max-w-3xl mx-auto leading-relaxed px-2 sm:px-4" style={{ color: '#717171' }}>
                    We're a Design Research and Innovation Learning Lab helping brands turn insights into unforgettable user journeys.
                  </p>
                </>
              )}

              {/* Slide 2 - Smart Research */}
              {activeSlide === 1 && (
                <>
                  <div className="text-center text-[#FF6347] font-ubuntu font-bold text-[28px] sm:text-[40px] md:text-[52px] mb-[20px]">
                    
                  </div>
                  <h1 className="font-ubuntu text-[32px] sm:text-[40px] md:text-[48px] font-bold leading-tight tracking-tight">
                    Smart Research.
                    <br />
                    <span className="mt-[10px] md:mt-[18px] inline-block text-[32px] sm:text-[40px] md:text-[48px]">Smarter <span className="text-white inline-flex items-center justify-center w-[200px] h-[56px] sm:w-[230px] sm:h-[60px] md:w-[263px] md:h-[66px] text-[32px] sm:text-[40px] md:text-[48px] font-ubuntu font-bold" style={{ borderRadius: '21px', background: 'linear-gradient(to bottom, #A03FFF, #CE9BFF)' }}>Outcomes</span></span>
                  </h1>
                  <p className="font-ubuntu text-[16px] sm:text-[18px] md:text-[20px] mt-[20px] md:mt-[36px] mb-[30px] md:mb-[54px] max-w-3xl mx-auto leading-relaxed px-4" style={{ color: '#717171' }}>
                    Our research identifies gaps, refines journeys, and delivers tailored UX
                    <br className="hidden sm:block" />
                    strategies that drive results.
                  </p>
                </>
              )}

              {/* Slide 3 - Crafting UX */}
              {activeSlide === 2 && (
                <>
                  <div className="text-center text-[#FF6347] font-ubuntu font-bold text-[28px] sm:text-[40px] md:text-[52px] mb-[20px]">
                    
                  </div>
                  <h1 className="font-ubuntu text-[32px] sm:text-[40px] md:text-[48px] font-bold leading-tight tracking-tight">
                    <span className="block sm:inline">Crafting UX With Precision</span>
                    <br />
                    <span className="mt-[10px] md:mt-[18px] inline-block text-[32px] sm:text-[40px] md:text-[48px]">And <span className="text-white inline-flex items-center justify-center w-[180px] h-[56px] sm:w-[190px] sm:h-[60px] md:w-[213px] md:h-[66px] text-[32px] sm:text-[40px] md:text-[48px] font-ubuntu font-bold" style={{ borderRadius: '21px', background: 'linear-gradient(to bottom, #A03FFF, #CE9BFF)' }}>Purpose</span></span>
                  </h1>
                  <p className="font-ubuntu text-[16px] sm:text-[18px] md:text-[20px] mt-[20px] md:mt-[36px] mb-[30px] md:mb-[54px] max-w-3xl mx-auto leading-relaxed px-4" style={{ color: '#717171' }}>
                    Our method is meticulous. Our results are meaningful. DRILL's approach
                    <br className="hidden sm:block" />
                    ensures every touchpoint works harder for your brand.
                  </p>
                </>
              )}
              
              {/* Download Button - visible on all slides */}
              <div className="flex justify-center mb-12 md:mb-24 px-4">
                <button
                  onClick={() => setIsDownloadDialogOpen(true)}
                  className="font-ubuntu text-sm md:text-[18px] flex items-center justify-center gap-3 md:gap-[16px] bg-black text-white hover:bg-gray-800 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300 mx-auto font-medium w-full max-w-[328px] h-12 sm:h-[50px] md:w-[328px] md:h-[50px] rounded-full"
                >
                <img
                    src="/download.svg"
                    alt="Download"
                    width={18}
                    height={18}
                    className="text-white md:w-[25px] md:h-[25px]"
                  />
                  Download Research Sample
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Pagination sliders */}
        <div className="absolute top-[65%] sm:top-[62%] left-1/2 -translate-x-1/2 flex items-center">
          <div 
            onClick={() => setActiveSlide(0)}
            className={`rounded-[18px] mx-[6px] transition-all duration-300 cursor-pointer ${
              activeSlide === 0 
                ? 'h-[6px] w-[40px] bg-black' 
                : 'h-[6px] w-[68px] bg-transparent border border-gray-400'
            }`}
          ></div>
          <div 
            onClick={() => setActiveSlide(1)}
            className={`rounded-[18px] mx-[6px] transition-all duration-300 cursor-pointer ${
              activeSlide === 1 
                ? 'h-[6px] w-[40px] bg-black' 
                : 'h-[6px] w-[68px] bg-transparent border border-gray-400'
            }`}
          ></div>
          <div 
            onClick={() => setActiveSlide(2)}
            className={`rounded-[18px] mx-[6px] transition-all duration-300 cursor-pointer ${
              activeSlide === 2 
                ? 'h-[6px] w-[40px] bg-black' 
                : 'h-[6px] w-[68px] bg-transparent border border-gray-400'
            }`}
          ></div>
        </div>
      </section>

      {/* Background gradient container - starts after animation */}
      <div 
        className="absolute w-full z-[-1]" 
        style={{ 
          top: '100vh', 
          bottom: '0',
          background: '#FFFFFF'
        }}
      ></div>

      {/* Why Do You Need DRILL Section */}
      <section id="why-drill" className="mt-16 md:mt-[146px] py-12 md:py-24 relative" ref={featuresSectionRef}>
        <h2 className="text-xl sm:text-2xl md:text-4xl font-bold text-center mb-8 md:mb-16 font-ubuntu px-4">
          Why Do You Need <span style={{ color: "#BC77FF" }}>DRILL?</span>
        </h2>

        <div className="max-w-[1300px] mx-auto px-4">
          {/* Desktop: Show all cards in grid */}
          <div className="hidden md:grid grid-cols-3 gap-12">
            {features.map((feature, i) => (
              <FeatureCard 
                key={i} 
                title={feature.title} 
                description={feature.description} 
                iconSrc={feature.icon} 
                isVisible={featuresVisible}
              />
            ))}
          </div>

          {/* Mobile: Show one card with pagination */}
          <div className="md:hidden">
            <FeatureCard 
              title={features[mobileFeatureIndex].title} 
              description={features[mobileFeatureIndex].description} 
              iconSrc={features[mobileFeatureIndex].icon} 
              isVisible={featuresVisible}
              isMobile={true}
              currentIndex={mobileFeatureIndex}
              totalCards={features.length}
              onNext={handleFeatureNext}
              onPrev={handleFeaturePrev}
            />
          </div>
        </div>
      </section>

      {/* Endless Possibilities Section - Using the new component */}
      <section className="relative z-10 mt-[-30px] md:mt-[-50px]">
      <ResearchGrid />
      </section>

      {/* Add spacing for footer */}
      <div className="h-[60px] md:h-[110px]"></div>

      <DownloadReportDialog
        isOpen={isDownloadDialogOpen}
        onClose={() => setIsDownloadDialogOpen(false)}
        onSubmit={async (data) => {
          console.log("Form data:", data)
          setIsDownloadDialogOpen(false)
        }}
      />
    </div>
  )
}
