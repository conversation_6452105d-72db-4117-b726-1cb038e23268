"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"

const services = [
  {
    title: "User Research",
    description: "Understand the driving force behind your user and steer it to your advantage.",
    image: "/User Research.svg",
    link: "/services/user-research",
  },
  {
    title: "UX Audit",
    description: "Assess what works and doesn't work against standard heuristics.",
    image: "/UX Audit.svg",
    link: "/services/ux-audit",
  },
  {
    title: "Eye Tracking Test",
    description: "Beat other tedious forms of research with our eye-tracking session. You get far more accurate and authentic results for innovation.",
    image: "/Eye Tracking.svg",
    link: "/services/eye-tracking",
  },
  {
    title: "Usability Testing",
    description: "Your product can be usable and functional simultaneously.",
    image: "/Usability Testing.svg",
    link: "/services/usability-testing",
  },
  {
    title: "Live Session Analysis",
    description: "You do not want to miss out on user behaviour in real-time.",
    image: "/Live Session Analysis.svg",
    link: "/services/live-session",
  },
  {
    title: "Persona Development",
    description: "Nothing beats empathizing with your users to solve their problems. Try persona development with DRILL Today.",
    image: "/Persona Development.svg",
    link: "/services/persona-development",
  },
  {
    title: "Competitive Analysis",
    description: "Stay ahead of the curve and unlock your innovation potential.",
    image: "/Competitive Analysis.svg",
    link: "/services/competitive-analysis",
  },
]

export default function ServicesPage() {
  const [hoveredService, setHoveredService] = useState<number | null>(null)

  // Add this useEffect to scroll to top when the page loads
  useEffect(() => {
    window.scrollTo(0, 0)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Decorative Elements */}
      <div className="hidden md:block absolute top-12 left-1/4 w-8 h-8 bg-purple-500 rounded-full opacity-20" />
      <div className="hidden md:block absolute top-16 right-1/4 w-6 h-6 bg-purple-500 rounded-full opacity-20" />

      <div className="container mx-auto px-4">
        <div className="text-center mt-16 md:mt-[80px] mb-16 md:mb-[140px] px-4">
          <h1 className="font-ubuntu text-2xl sm:text-3xl md:text-[48px] font-bold mb-4">
            Our <span style={{ color: "#BC77FF" }}>Services</span>
          </h1>
          <p className="font-ubuntu text-sm sm:text-base md:text-[20px] font-bold" style={{ color: "#717171" }}>
            Get 2X more accurate research output<br className="md:hidden" /> with DRILL today!
          </p>
          <div className="mt-8 md:mt-[80px] flex justify-center">
            <Link href="/contact">
              <button className="w-full max-w-[185px] h-12 md:h-[50px] bg-black text-white hover:bg-[#333333] hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300 rounded-full text-sm md:text-[16px] font-bold flex items-center justify-center gap-2">
                <span>Get in touch</span>
                <Image
                  src="/cta.png"
                  alt="CTA"
                  width={30}
                  height={30}
                  className="md:w-[38px] md:h-[38px]"
                />
              </button>
            </Link>
          </div>
        </div>

        <div className="mt-8 md:mt-[140px] max-w-[1300px] mx-auto">
          {/* First row with three cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[12px] mb-8 md:mb-[60px]">
            {services.slice(0, 3).map((service, i) => (
            <div key={i} className="relative">
              <Link
                href={service.link}
                  className="block"
                onMouseEnter={() => setHoveredService(i)}
                onMouseLeave={() => setHoveredService(null)}
              >
                  <div className="relative w-full max-w-[387px] mx-auto h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group">
                    {/* Skeleton background */}
                    <div
                      className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
                      style={{
                        backgroundImage: `url('/skeleton.svg')`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        zIndex: 1,
                      }}
                    />

                    {/* Main card container with hover tilt */}
                    <div
                      className="absolute inset-0 transition-all duration-300"
                      style={{
                        transform: hoveredService === i ? "rotate(-6.41deg)" : "rotate(0deg)",
                        transformOrigin: "center center",
                        zIndex: 2,
                      }}
                    >
                      {/* Card content */}
                      <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                        {/* Background image */}
                        <Image 
                          src={service.image} 
                          alt={service.title} 
                          fill 
                          className="object-cover"
                        />

                        {/* Gradient overlays */}
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                          style={{
                            backgroundImage: `url('/grey gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i ? 0 : 0.7,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300 md:block"
                          style={{
                            backgroundImage: `url('/purple gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i ? 0.7 : 0,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />

                        {/* Text content */}
                        <div className="absolute top-4 left-4 md:top-8 md:left-8 z-20">
                          <h3 className="text-white text-lg md:text-[28px] font-bold mb-2 md:mb-3 whitespace-pre-line">{service.title}</h3>
                          <div
                            className={`max-w-[85%] md:max-w-[80%] text-white text-sm md:text-base transition-all duration-300 ${
                              hoveredService === i ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
                            }`}
                          >
                            <p>{service.description}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Arrow Button */}
                <div className="absolute bottom-[-5px] md:bottom-[-10px] right-0 z-10">
                  <Image
                    src={hoveredService === i ? "/black_arrow.svg" : "/purple_arrow.svg"}
                    alt="Arrow"
                    width={60}
                    height={60}
                    className="md:w-[90px] md:h-[90px] transition-all duration-300"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Second row with three cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[12px] mb-8 md:mb-[60px]">
            {services.slice(3, 6).map((service, i) => (
              // Same card component as above
              <div key={i + 3} className="relative">
                <Link
                  href={service.link}
                  className="block"
                  onMouseEnter={() => setHoveredService(i + 3)}
                  onMouseLeave={() => setHoveredService(null)}
                >
                  <div className="relative w-full max-w-[387px] mx-auto h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group">
                    {/* Skeleton background */}
                    <div
                      className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
                      style={{
                        backgroundImage: `url('/skeleton.svg')`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        zIndex: 1,
                      }}
                    />

                    {/* Main card container with hover tilt */}
                    <div
                      className="absolute inset-0 transition-all duration-300"
                      style={{
                        transform: hoveredService === i + 3 ? "rotate(-6.41deg)" : "rotate(0deg)",
                        transformOrigin: "center center",
                        zIndex: 2,
                      }}
                    >
                      {/* Card content */}
                      <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                        {/* Background image */}
                        <Image 
                          src={service.image} 
                          alt={service.title} 
                          fill 
                          className="object-cover"
                        />

                        {/* Gradient overlays */}
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                          style={{
                            backgroundImage: `url('/grey gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i + 3 ? 0 : 0.7,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                          style={{
                            backgroundImage: `url('/purple gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i + 3 ? 0.7 : 0,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />

                        {/* Text content */}
                        <div className="absolute top-4 left-4 md:top-8 md:left-8 z-20">
                          <h3 className="text-white text-lg md:text-[28px] font-bold mb-2 md:mb-3 whitespace-pre-line">{service.title}</h3>
                          <div
                            className={`max-w-[85%] md:max-w-[80%] text-white text-sm md:text-base transition-all duration-300 ${
                              hoveredService === i + 3 ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
                            }`}
                          >
                            <p>{service.description}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* Arrow Button */}
                <div className="absolute bottom-[-5px] md:bottom-[-10px] right-0 z-10">
                  <Image
                    src={hoveredService === i + 3 ? "/black_arrow.svg" : "/purple_arrow.svg"}
                    alt="Arrow"
                    width={60}
                    height={60}
                    className="md:w-[90px] md:h-[90px] transition-all duration-300"
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Third row with one card */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[12px] mb-16 md:mb-[110px]">
            {services.slice(6).map((service, i) => (
              // Same card component as above
              <div key={i + 6} className="relative">
                <Link
                  href={service.link}
                  className="block"
                  onMouseEnter={() => setHoveredService(i + 6)}
                  onMouseLeave={() => setHoveredService(null)}
                >
                  <div className="relative w-full max-w-[387px] mx-auto h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group">
                    {/* Skeleton background */}
                    <div
                      className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
                      style={{
                        backgroundImage: `url('/skeleton.svg')`,
                        backgroundSize: "cover",
                        backgroundPosition: "center",
                        zIndex: 1,
                      }}
                    />

                    {/* Main card container with hover tilt */}
                    <div
                      className="absolute inset-0 transition-all duration-300"
                      style={{
                        transform: hoveredService === i + 6 ? "rotate(-6.41deg)" : "rotate(0deg)",
                        transformOrigin: "center center",
                        zIndex: 2,
                      }}
                    >
                      {/* Card content */}
                      <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                        {/* Background image */}
                        <Image 
                          src={service.image} 
                          alt={service.title} 
                          fill 
                          className="object-cover"
                        />

                        {/* Gradient overlays */}
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                          style={{
                            backgroundImage: `url('/grey gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i + 6 ? 0 : 0.7,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />
                        <div
                          className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                          style={{
                            backgroundImage: `url('/purple gradient.svg')`,
                            backgroundSize: "cover",
                            height: "250px",
                            opacity: hoveredService === i + 6 ? 0.7 : 0,
                            borderTopLeftRadius: "30px",
                            borderTopRightRadius: "30px",
                          }}
                        />

                        {/* Text content */}
                        <div className="absolute top-4 left-4 md:top-8 md:left-8 z-20">
                          <h3 className="text-white text-lg md:text-[28px] font-bold mb-2 md:mb-3 whitespace-pre-line">{service.title}</h3>
                          <div
                            className={`max-w-[85%] md:max-w-[80%] text-white text-sm md:text-base transition-all duration-300 ${
                              hoveredService === i + 6 ? "opacity-100 translate-y-0" : "opacity-0 -translate-y-4"
                            }`}
                          >
                            <p>{service.description}</p>
                          </div>
                        </div>
                      </div>
                  </div>
                </div>
              </Link>

                {/* Arrow Button */}
                <div className="absolute bottom-[-5px] md:bottom-[-10px] right-0 z-10">
                  <Image
                    src={hoveredService === i + 6 ? "/black_arrow.svg" : "/purple_arrow.svg"}
                    alt="Arrow"
                    width={60}
                    height={60}
                    className="md:w-[90px] md:h-[90px] transition-all duration-300"
                  />
                </div>
            </div>
          ))}
          </div>
        </div>
      </div>
    </div>
  )
}
