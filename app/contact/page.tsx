"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import Image from "next/image"
import SuccessDialog from "@/components/success-dialog"

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    phone: "",
    email: "",
    interest: "",
    message: "",
    companyName: "",
  })
  const [isSuccessModalOpen, setIsSuccessModalOpen] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log(formData)
    setIsSuccessModalOpen(true)
  }

  const isFieldFilled = (field: string) => {
    return formData[field as keyof typeof formData]?.length > 0
  }

  // All services from the header component
  const services = [
    { name: "User Research", value: "user-research" },
    { name: "UX Audit", value: "ux-audit" },
    { name: "Eye Tracking Test", value: "eye-tracking" },
    { name: "Usability Testing", value: "usability-testing" },
    { name: "Live Session Analysis", value: "live-session" },
    { name: "Persona Development", value: "persona-development" },
    { name: "Competitive Analysis", value: "competitive-analysis" },
    { name: "Other", value: "other" },
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="container mx-auto px-4 pt-16 md:pt-[94px] text-center">
        <h1 className="text-2xl sm:text-3xl md:text-[48px] font-ubuntu font-bold mb-4">
          Have Some <span className="text-[#BC77FF]">Queries</span>
        </h1>
        <p className="text-[#717171] font-ubuntu text-sm sm:text-base md:text-[20px] font-bold mt-7 px-4">
          Please feel free to write us for support and inquiries by using <br />the form below. Your feedback also matters.
        </p>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 mt-12 md:mt-[140px] mb-16 md:mb-[110px]">
        <div className="bg-[#F5F6FA] rounded-[30px] md:rounded-[60px] p-6 md:p-12 shadow-md w-full max-w-[1200px] h-auto md:h-[795px] mx-auto">
          <div className="flex flex-col lg:flex-row gap-8 md:gap-12 h-full">
            {/* Contact Information */}
            <div className="relative w-full lg:w-auto">
              {/* White Contact Information Card */}
              <div className="bg-white rounded-[30px] md:rounded-[60px] p-6 md:p-9 shadow-lg w-full lg:w-[566px] h-auto lg:h-[710px] flex flex-col">
                <h2 className="text-xl md:text-3xl font-ubuntu font-bold mb-4">Contact Information</h2>
                <p className="text-[#717171] font-ubuntu font-normal mb-8 md:mb-10 text-sm md:text-base">
                  We'll available for your queries. Contact us any time on below info.
                </p>

                  <div className="space-y-6 md:space-y-8">
                    {/* Phone and SMS - stacked on mobile, side by side on desktop */}
                    <div className="flex flex-col md:flex-row md:items-center gap-4 md:gap-[65px]">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 md:w-12 md:h-12 flex items-center justify-center">
                          <Image src="/call.svg" alt="Call" width={20} height={20} className="md:w-6 md:h-6" />
                        </div>
                        <a href="tel:+919874991222" className="text-gray-700 hover:text-purple-500 text-sm md:text-lg font-ubuntu font-normal">
                          +91 9874991222
                        </a>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 md:w-12 md:h-12 flex items-center justify-center">
                          <Image src="/sms.svg" alt="SMS" width={20} height={20} className="md:w-6 md:h-6" />
                        </div>
                        <a href="mailto:<EMAIL>" className="text-gray-700 hover:text-purple-500 text-sm md:text-lg font-ubuntu font-normal">
                          <EMAIL>
                        </a>
                      </div>
                    </div>

                    {/* Location */}
                    <div className="flex items-start gap-2 mt-6 md:mt-8">
                      <div className="w-8 h-8 md:w-12 md:h-12 flex items-center justify-center">
                        <Image src="/location.svg" alt="Location" width={20} height={20} className="md:w-6 md:h-6" />
                      </div>
                      <div className="text-gray-700 text-sm md:text-lg font-ubuntu font-normal">
                      4th floor, Spectrum Tower, Chincholi<br />
                      Bunder Road Malad (West),<br />
                      Mumbai 400064
                    </div>
                  </div>
                </div>

                {/* Video Animation - positioned at bottom right */}
                <div className="flex-1 flex items-end justify-center lg:justify-end mt-6 lg:mt-0">
                  <video
                    autoPlay
                    muted
                    playsInline
                    preload="auto"
                    className="w-48 h-56 md:w-[337px] md:h-[373px] object-contain"
                  >
                    <source src="/Contact_us.mp4" type="video/mp4" />
                  </video>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <div className="flex-1 contact-form w-full lg:w-auto">
              <h2 className="text-xl md:text-3xl font-ubuntu font-bold mb-6 md:mb-10">Get In Touch With Us</h2>
              <form onSubmit={handleSubmit} className="space-y-8">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-[#717171] font-ubuntu font-normal">
                    Name <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "name" || isFieldFilled("name") ? "border-purple-500" : "border-gray-200"
                    } pb-2 transition-colors duration-200`}
                  >
                    <input
                      id="name"
                      className="w-full bg-transparent focus:outline-none focus:ring-0 font-ubuntu font-normal"
                      placeholder="Your full name"
                      value={formData.name}
                      onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      onFocus={() => setFocusedField("name")}
                      onBlur={() => setFocusedField(null)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone" className="text-[#717171] font-ubuntu font-normal">
                    Phone Number <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "phone" || isFieldFilled("phone") ? "border-purple-500" : "border-gray-200"
                    } pb-2 transition-colors duration-200`}
                  >
                    <input
                      id="phone"
                      type="tel"
                      className="w-full bg-transparent focus:outline-none focus:ring-0 font-ubuntu font-normal"
                      placeholder="Your phone number"
                      value={formData.phone}
                      onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      onFocus={() => setFocusedField("phone")}
                      onBlur={() => setFocusedField(null)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-[#717171] font-ubuntu font-normal">
                    Email <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "email" || isFieldFilled("email") ? "border-purple-500" : "border-gray-200"
                    } pb-2 transition-colors duration-200`}
                  >
                    <input
                      id="email"
                      type="email"
                      className="w-full bg-transparent focus:outline-none focus:ring-0 font-ubuntu font-normal"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      onFocus={() => setFocusedField("email")}
                      onBlur={() => setFocusedField(null)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="interest" className="text-[#717171] font-ubuntu font-normal">
                    Interest <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "interest" || isFieldFilled("interest") ? "border-purple-500" : "border-gray-200"
                    } transition-colors duration-200`}
                  >
                    <Select
                      value={formData.interest}
                      onValueChange={(value) => setFormData({ ...formData, interest: value })}
                      onOpenChange={(open) => {
                        setIsDropdownOpen(open)
                        setFocusedField(open ? "interest" : null)
                      }}
                    >
                      <SelectTrigger className="w-full bg-transparent border-0 p-0 pb-2 focus:ring-0 focus:ring-offset-0 font-ubuntu font-normal">
                        <SelectValue placeholder="Select your interest area" />
                        <Image
                          src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/arrow-down-gutHyAKzAJIETXPkWu3WdGRm6VxLBA.png"
                          alt="Dropdown"
                          width={24}
                          height={24}
                          className={`opacity-50 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : 'rotate-0'}`}
                        />
                      </SelectTrigger>
                      <SelectContent className="bg-white font-ubuntu">
                          <SelectItem value="user-research">User Research</SelectItem>
                          <SelectItem value="eye-tracking">Eye Tracking Test</SelectItem>
                          <SelectItem value="ux-audit">UX Audit</SelectItem>
                          <SelectItem value="usability-testing">Usability Testing</SelectItem>
                          <SelectItem value="live-session">Live session Analysis</SelectItem>
                          <SelectItem value="persona-development">Persona Development</SelectItem>
                          <SelectItem value="competitive-analysis">Competitive Analysis</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                     </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className="text-[#717171] font-ubuntu font-normal">
                    Anything else you'd like us to know? <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "message" || isFieldFilled("message") ? "border-purple-500" : "border-gray-200"
                    } pb-2 transition-colors duration-200`}
                  >
                    <input
                      id="message"
                      className="w-full bg-transparent focus:outline-none focus:ring-0 font-ubuntu font-normal"
                      placeholder="Start typing here"
                      value={formData.message}
                      onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                      onFocus={() => setFocusedField("message")}
                      onBlur={() => setFocusedField(null)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="companyName" className="text-[#717171] font-ubuntu font-normal">
                    Company Name <span className="text-red-500">*</span>
                  </Label>
                  <div
                    className={`border-b-2 ${
                      focusedField === "companyName" || isFieldFilled("companyName") ? "border-purple-500" : "border-gray-200"
                    } pb-2 transition-colors duration-200`}
                  >
                    <input
                      id="companyName"
                      className="w-full bg-transparent focus:outline-none focus:ring-0 font-ubuntu font-normal"
                      placeholder="Your company name"
                      value={formData.companyName}
                      onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                      onFocus={() => setFocusedField("companyName")}
                      onBlur={() => setFocusedField(null)}
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-8">
                  <Button
                    type="submit"
                    className="btn-submit bg-black text-white"
                  >
                    Submit
                    <Image
                      src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vector-NruOIe9b7aoZD0GcrxrxwnX9Y0xqVK.svg"
                      alt="Submit"
                      width={38}
                      height={38}
                    />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      <SuccessDialog isOpen={isSuccessModalOpen} onClose={() => setIsSuccessModalOpen(false)} />
    </div>
  )
}
