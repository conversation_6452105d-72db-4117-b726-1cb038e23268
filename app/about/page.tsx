"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"

interface TeamMember {
  name: string
  role: string
  image: string
}

const teamMembers: TeamMember[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Director & CEO",
    image:
      "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Kishor-Pic-1%203.jpg-4ovHRs9KyjfwWPkjVIJX4DCnUlgeiT.jpeg",
  },
  {
    name: "<PERSON>",
    role: "CTO",
    image: "/placeholder.svg?height=300&width=300",
  },
  {
    name: "<PERSON>",
    role: "Head of Research",
    image: "/placeholder.svg?height=300&width=300",
  },
]

const facilities = [
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/4.jpg-pOjHw2q22e7qg4cMJtpfbTmxL77Y0g.jpeg",
    alt: "Clean desk setup with laptop",
  },
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/5.jpg-tWg8qQIBOCFLdo7mWuZYKZI5NAbHKB.jpeg",
    alt: "Team brainstorming session",
  },
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/6.jpg-EJVdz9e8Qc3LwRrLDhoTLRafyGv72R.jpeg",
    alt: "Design planning session",
  },
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/7.jpg-yQuRNEkeDVWvJveECLatJe4P9G8dYx.jpeg",
    alt: "Modern office workspace",
  },
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/8.jpg-f3NyqSwFus8arLhsX7M8ytFfJQ7GB6.jpeg",
    alt: "Conference room",
  },
  {
    image: "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9.jpg-2ovlwEb9vxtlYJzXrxKjW797MES9y9.jpeg",
    alt: "Team collaboration at desk",
  },
]

const psychologyCards = [
  {
    title: "Insights into User Behavior",
    description: "UX labs help businesses gain perception into user behavior by observing their preferences, pain points, and behaviors.",
    videoSrc: "/Insight into User Behavior.mp4"
  },
  {
    title: "Monitoring Performance",
    description: "UX labs continuously monitor product/service performance, ensuring optimal user experiences.",
    videoSrc: "/Monitoring Performance.mp4"
  },
  {
    title: "Data Driven Decision Making",
    description: "With UX Labs data insights, businesses can make informed decisions for product development, marketing strategies, etc.",
    videoSrc: "/Data-Driven Decision Making.mp4"
  },
  {
    title: "Measuring Success",
    description: "UX labs track design changes' impact on user satisfaction, engagement, and critical metrics, ensuring business success.",
    videoSrc: "/Measuring Success.mp4"
  }
]

export default function AboutPage() {
  const [hoveredMember, setHoveredMember] = useState<string | null>(null)
  const [activeFacilityIndex, setActiveFacilityIndex] = useState(0)

  // Continuous loop animation for facilities
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFacilityIndex((prev) => (prev + 1) % 6) // Loop through 0-5
    }, 800) // Change every 800ms

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen">
      {/* Mission Section */}
      <section className="container mx-auto px-4 text-center pt-16 md:pt-[105px]">
        <h1 className="font-ubuntu text-2xl md:text-[48px] font-bold mb-4">
          Our <span style={{ color: "#BC77FF" }}>Mission</span>
        </h1>
        <p className="text-gray-700 text-sm md:text-base max-w-2xl font-bold mx-auto">Help build better products with research within half the time</p>
      </section>

      {/* Founder's Vision */}
      <section className="container mx-auto px-4 pt-16 md:pt-[100px]">
        <div className="bg-[#F5F6FA] rounded-[30px] md:rounded-[60px] shadow-lg mx-auto flex flex-col lg:flex-row items-center w-full max-w-[1200px] p-6 md:p-0" style={{ minHeight: '538px' }}>
          <div className="flex flex-col lg:flex-row items-center w-full h-full">
            <div className="flex-1 lg:pl-16 lg:pr-8 text-center lg:text-left">
              <h2 className="font-ubuntu text-xl md:text-[48px] font-bold mb-10 text-black">Founders Vision</h2>
              <h3 className="font-ubuntu text-lg md:text-[32px] font-medium mb-5 text-black">Kishor Fogla</h3>
              <p className="text-base md:text-[20px] mb-6" style={{ color: "#BC77FF" }}>Founder & CEO</p>
              <p className="text-sm md:text-[24px] text-[#727272] leading-relaxed">
                At our UX Lab, we envision creating a premier research space where companies can seamlessly conduct user experiments like interviews and testing. Our goal is to empower businesses to rapidly validate their designs with ease, without the need for their own infrastructure, fostering innovation and enhancing user experience across industries.
              </p>
            </div>
            <div className="flex-shrink-0 mt-6 lg:mt-0 lg:pr-5">
              <div className="relative w-[280px] h-[300px] md:w-[464px] md:h-[489px] rounded-[30px] md:rounded-[60px] overflow-hidden mx-auto">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Rectangle%2034624357.jpg-Aj11wVRxa7ebOXuxNLAGRVnbyEUAay.jpeg"
                  alt="Kishor Fogla"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Psychology of DRILL Section */}
      <section className="pt-16 md:pt-[150px]">
        <div className="container mx-auto px-4">
          <div className="max-w-[1200px] mx-auto">
            <h2 className="text-center text-xl md:text-[40px] font-ubuntu font-bold mb-8 md:mb-[78px]">
              Psychology Of <span style={{ color: "#BC77FF" }}>DRILL</span>
            </h2>

            <div className="flex flex-col lg:flex-row lg:flex-wrap justify-center gap-4 md:gap-[20px]">
              {psychologyCards.map((card, index) => (
                <div 
                  key={card.title}
                  className="bg-[#F5F6FA] flex flex-col md:flex-row items-center w-full max-w-[590px] max-h-[210px] mx-auto p-6 md:p-[40px] rounded-[30px] md:rounded-[60px]"
                  style={{ 
                    minHeight: '200px'
                  }}
                >
                  {/* Text Content */}
                  <div className="flex-1 md:pr-8 text-center md:text-left">
                    <h3 className="font-ubuntu text-base md:text-[18px] font-bold mb-4 text-black">{card.title}</h3>
                    <p className="text-gray-600 text-sm md:text-[16px] leading-relaxed">
                      {card.description}
                    </p>
                  </div>

                  {/* Animation Container */}
                  <div className="flex items-center justify-center flex-shrink-0 w-[120px] h-[120px] md:w-[169px] md:h-[187px] mt-4 md:mt-0">
                    <video
                      muted
                      playsInline
                      className="w-full h-full object-contain"
                      onLoadedData={(e) => {
                        const video = e.target as HTMLVideoElement;
                        const observer = new IntersectionObserver(
                          (entries) => {
                            entries.forEach((entry) => {
                              if (entry.isIntersecting) {
                                video.play();
                                observer.disconnect(); // Play only once
                              }
                            });
                          },
                          { threshold: 0.5 }
                        );
                        observer.observe(video);
                      }}
                    >
                      <source src={card.videoSrc} type="video/mp4" />
                    </video>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Facilities */}
      <section className="container mx-auto px-4 pt-16 md:pt-[155px]">
        <div className="text-center mb-8 md:mb-16">
          <h2 className="text-xl md:text-[40px] font-ubuntu font-bold mb-4">
            Our <span style={{ color: "#BC77FF" }}>Facilities</span>
          </h2>
          <p className="text-[#717171] text-sm md:text-[20px] font-ubuntu max-w-3xl mx-auto">
            We provide a large range of equipments and facilities that can help enhance the quality of your research while reducing the time required
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-5xl mx-auto">
          {/* Column 1 */}
          <div className="space-y-4">
            <div className={`relative w-full h-[250px] md:h-[403px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 0 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities1.svg"
                alt="Facilities1"
                fill
                className="object-cover"
              />
            </div>
            <div className={`relative w-full h-[180px] md:h-[286px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 3 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities4.svg"
                alt="Facilities4"
                fill
                className="object-cover"
              />
            </div>
          </div>
          {/* Column 2 */}
          <div className="space-y-4">
            <div className={`relative w-full h-[130px] md:h-[214px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 1 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities2.svg"
                alt="Facilities2"
                fill
                className="object-cover"
              />
            </div>
            <div className={`relative w-full h-[300px] md:h-[475px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 4 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities5.svg"
                alt="Facilities5"
                fill
                className="object-cover"
              />
            </div>
          </div>
          {/* Column 3 */}
          <div className="space-y-4">
            <div className={`relative w-full h-[180px] md:h-[286px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 2 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities3.svg"
                alt="Facilities3"
                fill
                className="object-cover"
              />
            </div>
            <div className={`relative w-full h-[250px] md:h-[403px] rounded-[30px] md:rounded-[60px] overflow-hidden transition-all duration-700 ease-out ${
              activeFacilityIndex === 5 ? 'scale-100 z-10' : 'scale-95'
            }`}>
              <Image
                src="/Facilities6.svg"
                alt="Facilities6"
                fill
                className="object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Spacer for footer */}
      <div className="pb-16 md:pb-[110px]"></div>

      {/* Team Section - Hidden */}
    </div>
  )
}