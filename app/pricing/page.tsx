"use client"

import { ArrowR<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { useState, useEffect, useRef } from "react"

export default function Pricing() {
  const [isVisible, setIsVisible] = useState(false)
  const cardsRef = useRef<HTMLDivElement>(null)
  const comparisonRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true)
        }
      },
      {
        threshold: 0.1, // Trigger when 10% of the element is visible (reduced from 20%)
        rootMargin: '0px' // Trigger as soon as element enters viewport (reduced from -50px)
      }
    )

    if (cardsRef.current) {
      observer.observe(cardsRef.current)
    }

    return () => {
      if (cardsRef.current) {
        observer.unobserve(cardsRef.current)
      }
    }
  }, [isVisible])

  const packages = [
    {
      title: "Small\nOrganisation",
      tag: "FOR 30 DAYS",
      features: [
        { name: "Interview", included: true },
        { name: "Survey", included: true },
        { name: "Competitive Analysis", included: true },
        { name: "Heuristic Evaluation", included: true },
        { name: "5 Second Testing", included: false },
      ],
      highlighted: false,
    },
    {
      title: "Mid-Size\nOrganisation",
      tag: "FOR 30 DAYS",
      features: [
        { name: "Interview", included: true },
        { name: "Survey", included: true },
        { name: "Competitive Analysis", included: true },
        { name: "Heuristic Evaluation", included: true },
        { name: "5 Second Testing", included: true },
      ],
      highlighted: true,
    },
    {
      title: "Large\nOrganisation",
      tag: "FOR 30 DAYS",
      features: [
        { name: "Interview", included: true },
        { name: "Survey", included: true },
        { name: "Competitive Analysis", included: true },
        { name: "Heuristic Evaluation", included: true },
        { name: "5 Second Testing", included: true },
      ],
      highlighted: false,
    },
  ]

  const scrollToComparison = () => {
    comparisonRef.current?.scrollIntoView({ 
      behavior: 'smooth',
      block: 'start'
    })
  }

  return (
    <div className="min-h-screen">
      {/* Decorative Elements */}
      <div className="hidden md:block absolute top-12 left-1/4 w-8 h-8 bg-purple-500 rounded-full opacity-20" />
      <div className="hidden md:block absolute top-16 right-1/4 w-6 h-6 bg-purple-500 rounded-full opacity-20" />

      {/* Hero Section */}
      <section className="container mx-auto px-4 mt-16 md:mt-[80px] text-center relative">
        <div className="flex flex-col items-center">
          <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold flex flex-col">
            <span className="mb-4 md:mb-[20px]">
              Finely <span style={{ color: "#BC77FF" }}>Tuned Services</span>
            </span>
            <span>For Your Needs</span>
        </h1>
          <p className="font-ubuntu text-base md:text-[20px] font-bold text-[#717171] mt-6 md:mt-[33px]">Our Packages</p>
        </div>

        {/* Packages Grid */}
        <div ref={cardsRef} className="flex flex-col md:flex-row justify-center items-center md:items-start gap-6 md:gap-8 mt-12 md:mt-[140px]">
          {packages.map((pkg, i) => (
            <div
              key={i}
              className={`w-full max-w-[387px] ${
                i === 1 ? "h-auto min-h-[500px] md:h-[601px]" : "h-auto min-h-[450px] md:h-[535px]"
              } bg-white rounded-[40px] md:rounded-[60px] shadow-lg hover:shadow-xl transition-all duration-700 hover:scale-[1.02] relative ${
                i === 1 
                  ? "shadow-[0_4px_20px_0px_rgba(188,119,255,0.2)]" 
                  : "border border-[#DFDEDE]"
              } ${
                i === 1 ? "" : "md:mt-[33px]"
              } flex flex-col p-6 md:p-10 ${
                isVisible 
                  ? "opacity-100" 
                  : "opacity-0"
              }`}
              style={{
                transform: isVisible 
                  ? 'translateX(0px)' 
                  : `translateX(${
                      i === 0 ? '419px' : // Left card starts at middle position (387px + 32px gap)
                      i === 2 ? '-419px' : // Right card starts at middle position
                      '0px' // Middle card stays in place
                    })`,
                transitionDelay: isVisible ? (i === 1 ? '0ms' : i === 0 ? '100ms' : '200ms') : '0ms',
                transitionDuration: '500ms',
                transitionTimingFunction: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
              }}
            >
              <div>
                <div className="flex justify-between items-start">
                  <h3 className="text-lg md:text-[24px] font-ubuntu font-bold whitespace-pre-line leading-tight text-left text-[#191919]">{pkg.title}</h3>
                  <span className="bg-[#BC77FF] text-white text-xs md:text-[12px] w-20 md:w-[99px] h-6 md:h-[30px] flex items-center justify-center rounded-full font-ubuntu font-bold">{pkg.tag}</span>
                </div>
                <div className="h-[1px] bg-[#DFDEDE] mt-4 md:mt-[25px]"></div>
              </div>

              <div className="flex-1 flex flex-col justify-center mt-6 md:mt-8">
                <ul className={`${i === 1 ? 'space-y-4 md:space-y-6' : 'space-y-3 md:space-y-4'}`}>
                  {pkg.features.map((feature, j) => (
                    <li key={j} className="flex items-center text-[#717171] font-ubuntu text-sm md:text-[18px] font-regular">
                      {feature.name === "5 Second Testing" ? (
                        <div className="w-3 md:w-4 h-[2px] bg-[#717171] mr-3 md:mr-4" />
                      ) : feature.included ? (
                        <div className="w-3 md:w-4 h-3 md:h-4 rounded-full bg-[#A850FE] mr-3 md:mr-4" />
                      ) : (
                        <div className="w-3 md:w-4 h-[2px] bg-[#717171] mr-3 md:mr-4" />
                      )}
                      {feature.name}
                    </li>
                  ))}
                </ul>

                <button 
                  onClick={scrollToComparison}
                  className={`text-[#191919] font-ubuntu text-sm md:text-[18px] border-b border-[#191919] hover:text-[#BC77FF] hover:border-[#BC77FF] transition-colors w-fit bg-transparent cursor-pointer ${
                    i === 1 ? 'mt-8 md:mt-12' : 'mt-6 md:mt-8'
                  }`}
                >
                  View details
                </button>
              </div>

              <Link href="/contact" className="block mt-6 md:mt-8">
                  <button
                  className={`w-full max-w-[307px] h-12 md:h-[50px] rounded-full flex items-center justify-center gap-2 transition-all duration-300 ${
                    i === 1 
                      ? "btn-purchase-now" 
                      : "bg-[#F2F2F2] text-black hover:bg-gray-100 hover:shadow-lg hover:shadow-purple-500/20 font-ubuntu font-bold text-sm md:text-[18px]"
                  }`}
                >
                  Purchase Now
                  {i === 1 ? (
                  <Image
                    src="/white arrow.svg"
                    alt="Arrow"
                    width={30}
                    height={30}
                    className="md:w-[38px] md:h-[38px]"
                  />
                  ) : null}
                </button>
              </Link>
            </div>
          ))}
        </div>
      </section>

      {/* Looking for Package Section */}
      <section className="container mx-auto px-4 mt-16 md:mt-[140px] pb-16">
        <div className="bg-[#F5F6FA] rounded-[30px] md:rounded-[60px] w-full max-w-[1202px] h-auto md:h-[195px] mx-auto shadow-[0_4px_20px_0px_rgba(188,119,255,0.2)] hover:shadow-[0_6px_30px_0px_rgba(188,119,255,0.3)] transition-all duration-300">
          <div className="flex flex-col md:flex-row items-center justify-between h-full p-6 md:px-[70px] md:py-[53px] gap-6 md:gap-8">
            <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8 text-center md:text-left">
              <div className="relative w-32 h-32 md:w-[180px] md:h-[180px]">
                <video
                  src="/Looking for a package.mp4"
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <h3 className="font-ubuntu text-lg sm:text-xl md:text-[32px] font-bold">Looking For A Package<br className="md:hidden" /> Fine Tuned</h3>
                <p className="font-ubuntu text-lg sm:text-xl md:text-[32px] font-bold text-purple-500">For Your Needs?</p>
              </div>
            </div>
            <Link href="/customize" className="inline-block w-full md:w-auto">
              <button className="btn-customize-now w-full md:w-auto">
                Customize now
                <Image
                  src="/white arrow.svg"
                  alt="Arrow"
                  width={30}
                  height={30}
                  className="md:w-[38px] md:h-[38px]"
                />
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Compare Packages Section */}
      <section ref={comparisonRef} className="container mx-auto px-4 mt-16 md:mt-[140px] pb-16">
        <h2 className="font-ubuntu text-xl sm:text-2xl md:text-[40px] font-bold mb-8 md:mb-12 text-center">
          Compare our User Research <span className="text-[#BC77FF]">Packages</span>
        </h2>
        <div className="flex justify-center mb-8 overflow-x-auto">
          <div className="relative min-w-[800px] md:min-w-0">
            <Image
              src="/compare_pricing.svg"
              alt="Compare Pricing"
              width={1200}
              height={2248}
              className="object-contain w-full h-auto"
            />
            {/* Clickable overlay buttons for Enquire Now */}
            {/* Trial Plan Button */}
            <Link href="/contact" className="absolute top-[12%] left-[33.5%] z-10" style={{ width: '148px', height: '40px' }}>
              <button className="w-full h-full bg-[#F2F2F2] hover:bg-gray-300 hover:shadow-lg hover:shadow-purple-500/20 text-black font-ubuntu font-bold text-[14px] transition-all duration-300 cursor-pointer flex items-center justify-center" style={{ borderRadius: '30.4px' }}>
                Enquire Now
              </button>
            </Link>
            
            {/* Small Organisation Button */}
            <Link href="/contact" className="absolute top-[12%] left-[50%] z-10" style={{ width: '148px', height: '40px' }}>
              <button className="w-full h-full bg-[#F2F2F2] hover:bg-gray-300 hover:shadow-lg hover:shadow-purple-500/20 text-black font-ubuntu font-bold text-[14px] transition-all duration-300 cursor-pointer flex items-center justify-center" style={{ borderRadius: '30.4px' }}>
                Enquire Now
              </button>
            </Link>
            
            {/* Mid-Size Organisation Button */}
            <Link href="/contact" className="absolute top-[12%] left-[66.5%] z-10" style={{ width: '148px', height: '40px' }}>
              <button className="w-full h-full bg-[#F2F2F2] hover:bg-gray-300 hover:shadow-lg hover:shadow-purple-500/20 text-black font-ubuntu font-bold text-[14px] transition-all duration-300 cursor-pointer flex items-center justify-center" style={{ borderRadius: '30.4px' }}>
                Enquire Now
              </button>
            </Link>
            
            {/* Large Organisation Button */}
            <Link href="/contact" className="absolute top-[12%] left-[83%] z-10">
              <button className="btn-enquire-now-black">
                Enquire Now
                <Image
                  src="/white arrow.svg"
                  alt="Arrow"
                  width={30}
                  height={30}
                />
              </button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
