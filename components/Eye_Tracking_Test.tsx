"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON>R<PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import DownloadReportDialog from "@/components/download-report-dialog"

interface Eye_Tracking_Test_Props {
    Eye_Tracking_Test: {
    title: string
    description: string
    image: string
  }[]
}

export default function ServiceEye_Tracking_Test({ Eye_Tracking_Test }: Eye_Tracking_Test_Props) {
  const [hoveredMethod, setHoveredMethod] = useState<string | null>(null)
  const [isDownloadDialogOpen, setIsDownloadDialogOpen] = useState(false)

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="container mx-auto px-4 pt-8 md:pt-[40px] text-center">
        <h1 className="text-2xl md:text-[48px] font-ubuntu font-bold mb-4">
          Eye <span className="text-[#BC77FF]">Tracking</span> Test
        </h1>
        <p className="text-[#717171] text-sm md:text-[20px] font-ubuntu font-bold mx-auto mb-12 md:mb-[60px]">
          Track users actual attention with physical eye movement and gaze tracking
        </p>
        <div className="flex justify-center">
          <Link href="/contact">
            <button className="w-full max-w-[280px] h-12 md:w-[185px] md:h-[50px] bg-black text-white hover:bg-[#333333] hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300 rounded-full text-base md:text-[18px] font-ubuntu font-bold flex items-center justify-center">
              <span className="ml-3">Get in touch</span>
              <Image
                src="/cta.png"
                alt="CTA"
                width={20}
                height={20}
                className="ml-4 md:w-[38px] md:h-[38px]"
              />
            </button>
          </Link>
        </div>
      </section>

      {/* Process Steps */}
      <section className="container mx-auto px-4 py-8 md:py-16">
        <div className="text-center mb-12 md:mb-20">
          <h2 className="text-xl md:text-[48px] font-ubuntu font-bold mb-4">
            Get <span className="text-[#BC77FF]">Better Insights</span> About Your Product
          </h2>
          <p className="text-[#717171] text-sm md:text-base max-w-3xl mx-auto mb-8 md:mb-16">
            Eye tracking testing enables you to map user movement, gaze point, scan path and fixation through testing to understand their attention distribution with the real engagement through products
          </p>
        </div>
      </section>

      {/* Eye_Tracking_Test Methods */}
      <section className="container mx-auto px-4 py-4 md:py-8 -mt-8 md:-mt-[100px]">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-[20px] justify-items-center">
          {/* Mobile Device Card */}
          <div
            className="relative w-full max-w-[387px] h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group"
            onMouseEnter={() => setHoveredMethod("Mobile Device")}
            onMouseLeave={() => setHoveredMethod(null)}
          >
            {/* Skeleton background */}
            <div
              className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
              style={{
                backgroundImage: `url('/skeleton.svg')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                zIndex: 1,
              }}
            />

            {/* Main card container with hover tilt */}
            <div
              className="absolute inset-0 transition-all duration-300"
              style={{
                transform: hoveredMethod === "Mobile Device" ? "rotate(-6.41deg)" : "rotate(0deg)",
                transformOrigin: "center center",
                zIndex: 2,
              }}
            >
              {/* Card content */}
              <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                <Image 
                  src="/mobile device.svg" 
                  alt="Mobile Device" 
                  fill 
                  className="object-cover"
                />

                {/* Gradient overlays */}
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/grey gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Mobile Device" ? 0 : 0.7,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/purple gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Mobile Device" ? 0.7 : 0,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />

                {/* Text content */}
                <div className="absolute top-6 md:top-8 left-6 md:left-8 z-20">
                  <h3 className="text-white text-lg md:text-[28px] font-ubuntu font-bold mb-3">Mobile Device</h3>
                  <p
                    className={`text-white text-sm md:text-[20px] font-ubuntu transition-opacity duration-300 ${
                      hoveredMethod === "Mobile Device" ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Tobii Pro Card */}
          <div
            className="relative w-full max-w-[387px] h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group"
            onMouseEnter={() => setHoveredMethod("Tobii Pro")}
            onMouseLeave={() => setHoveredMethod(null)}
          >
            {/* Skeleton background */}
            <div
              className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
              style={{
                backgroundImage: `url('/skeleton.svg')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                zIndex: 1,
              }}
            />

            {/* Main card container with hover tilt */}
            <div
              className="absolute inset-0 transition-all duration-300"
              style={{
                transform: hoveredMethod === "Tobii Pro" ? "rotate(-6.41deg)" : "rotate(0deg)",
                transformOrigin: "center center",
                zIndex: 2,
              }}
            >
              {/* Card content */}
              <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                <Image 
                  src="/tobiipro.svg" 
                  alt="Tobii Pro" 
                  fill 
                  className="object-cover"
                />

                {/* Gradient overlays */}
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/grey gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Tobii Pro" ? 0 : 0.7,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/purple gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Tobii Pro" ? 0.7 : 0,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />

                {/* Text content */}
                <div className="absolute top-6 md:top-8 left-6 md:left-8 z-20">
                  <h3 className="text-white text-lg md:text-[28px] font-ubuntu font-bold mb-3">Tobii Pro</h3>
                  <p
                    className={`text-white text-sm md:text-[20px] font-ubuntu transition-opacity duration-300 ${
                      hoveredMethod === "Tobii Pro" ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Software Based Card */}
          <div
            className="relative w-full max-w-[387px] h-[300px] md:w-[387px] md:h-[440px] overflow-visible cursor-pointer group"
            onMouseEnter={() => setHoveredMethod("Software Based")}
            onMouseLeave={() => setHoveredMethod(null)}
          >
            {/* Skeleton background */}
            <div
              className="absolute inset-0 rounded-[30px] md:rounded-[40px] opacity-70"
              style={{
                backgroundImage: `url('/skeleton.svg')`,
                backgroundSize: "cover",
                backgroundPosition: "center",
                zIndex: 1,
              }}
            />

            {/* Main card container with hover tilt */}
            <div
              className="absolute inset-0 transition-all duration-300"
              style={{
                transform: hoveredMethod === "Software Based" ? "rotate(-6.41deg)" : "rotate(0deg)",
                transformOrigin: "center center",
                zIndex: 2,
              }}
            >
              {/* Card content */}
              <div className="relative w-full h-full rounded-[30px] md:rounded-[40px] overflow-hidden">
                <Image 
                  src="/Softwarebased.svg" 
                  alt="Software Based" 
                  fill 
                  className="object-cover"
                />

                {/* Gradient overlays */}
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/grey gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Software Based" ? 0 : 0.7,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />
                <div
                  className="absolute top-0 left-0 right-0 transition-opacity duration-300"
                  style={{
                    backgroundImage: `url('/purple gradient.svg')`,
                    backgroundSize: "cover",
                    height: "250px",
                    opacity: hoveredMethod === "Software Based" ? 0.7 : 0,
                    borderTopLeftRadius: "30px",
                    borderTopRightRadius: "30px",
                  }}
                />

                {/* Text content */}
                <div className="absolute top-6 md:top-8 left-6 md:left-8 z-20">
                  <h3 className="text-white text-lg md:text-[28px] font-ubuntu font-bold mb-3">Software Based</h3>
                  <p
                    className={`text-white text-sm md:text-[20px] font-ubuntu transition-opacity duration-300 ${
                      hoveredMethod === "Software Based" ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="container mx-auto px-4 mt-8 md:mt-[120px] pb-8 md:pb-16">
        <div className="bg-[#F5F6FA] rounded-[30px] md:rounded-[60px] w-full max-w-[1202px] min-h-[150px] md:h-[195px] mx-auto shadow-[0_4px_20px_0px_rgba(188,119,255,0.2)] hover:shadow-[0_6px_30px_0px_rgba(188,119,255,0.3)] transition-all duration-300">
          <div className="flex flex-col md:flex-row items-center justify-between h-full p-6 md:px-[70px] md:py-[53px] gap-4 md:gap-0">
            <div className="flex flex-col md:flex-row items-center gap-4 md:gap-8 text-center md:text-left">
              <div className="relative w-[120px] h-[120px] md:w-[180px] md:h-[180px] flex-shrink-0">
                <video
                  src="/Still thinking.mp4"
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-contain"
                />
              </div>
              <div>
                <div className="flex flex-col">
                  <h3 className="font-ubuntu text-xl md:text-[32px]">
                    <span className="text-black">Still </span>
                    <span className="text-purple-500">thinking?</span>
                  </h3>
                </div>
                <p className="font-ubuntu text-sm md:text-[16px] font-bold">
                  Schedule an enquiry with us in order to clear your doubts
                </p>
              </div>
            </div>
            <Link href="/contact">
              <Button className="btn-get-in-touch bg-black text-white w-full md:w-auto flex-shrink-0">
                Get in touch
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/Vector-NruOIe9b7aoZD0GcrxrxwnX9Y0xqVK.svg"
                  alt="Arrow"
                  width={20}
                  height={20}
                  className="md:w-[38px] md:h-[38px]"
                />
              </Button>
            </Link>
          </div>
        </div>
      </section>
      {/* Download Button - positioned on right side */}
      <div className="flex justify-center md:justify-end px-4 md:ml-10 mb-12 md:mb-24 max-w-[1280px] mx-auto">
        <button
          onClick={() => setIsDownloadDialogOpen(true)}
          className="font-ubuntu text-base md:text-[18px] flex items-center justify-center gap-3 md:gap-[16px] bg-black text-white hover:bg-gray-800 hover:shadow-lg hover:shadow-purple-500/20 transition-all duration-300 font-medium w-full max-w-[328px] h-12 md:h-[50px] rounded-[25px] md:rounded-[38px]"
        >
          <Image
            src="/download.svg"
            alt="Download"
            width={20}
            height={20}
            className="text-white md:w-[24px] md:h-[24px]"
          />
          Download Research Sample
        </button>
      </div>
      <DownloadReportDialog
        isOpen={isDownloadDialogOpen}
        onClose={() => setIsDownloadDialogOpen(false)}
        onSubmit={async (data) => {
          console.log("Form data:", data)
          setIsDownloadDialogOpen(false)
        }}
      />
    </div>
  )
} 