"use client"

import { useState, useEffect } from "react"

export default function BackgroundMesh() {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  return (
    <div className="absolute top-[-17px] left-0 right-0 bottom-0 -z-10">
      <div
        className="w-full h-[400px] md:h-[500px] lg:h-[700px] mx-auto bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: isMobile ? `url('/Mobile_mesh_bg.svg')` : `url('/Mesh bg.svg')`,
          marginTop: '20px',
          // Mobile-specific positioning and scaling
          ...(isMobile && {
            maxWidth: '100vw',
            backgroundSize: 'cover',
            backgroundPosition: 'center top',
            height: '500px'
          }),
          // Desktop positioning (preserve existing)
          ...(!isMobile && {
            maxWidth: '1405px',
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          })
        }}
      />
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/50 to-white" aria-hidden="true" />
    </div>
  )
}
