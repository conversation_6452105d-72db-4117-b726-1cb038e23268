"use client"

import { useRef, useEffect, useState } from "react"

export default function ScrollAnimation() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [loadError, setLoadError] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [isMobile, setIsMobile] = useState(false)

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    // Optimize video loading - load metadata first, then data on demand
    video.preload = "metadata"

    // Load the video
    video.load()

    // Handle video loading
    const handleVideoLoaded = () => {
      console.log("Video loaded successfully")
      setIsVideoLoaded(true)
      // Pause at the beginning
      video.currentTime = 0
      video.pause()
    }

    const handleLoadError = (e: Event) => {
      console.error("Error loading video:", e)
      setLoadError(true)
    }

    video.addEventListener("loadeddata", handleVideoLoaded)
    video.addEventListener("error", handleLoadError)

    // Handle scroll to control video playback with improved throttling
    let lastScrollTime = 0
    let ticking = false
    const throttleDelay = 16 // ms (60fps)

    const handleScroll = () => {
      const now = Date.now()
      if (now - lastScrollTime < throttleDelay) return
      lastScrollTime = now

      if (!ticking) {
        requestAnimationFrame(() => {
          if (!video || !isVideoLoaded) {
            ticking = false
            return
          }

          // Calculate how far we've scrolled into the page
          const scrollTop = window.scrollY
          const maxScrollHeight = 1000 // Animation plays only for first 1000px

          // Hide animation after viewport height scroll
          const windowHeight = window.innerHeight
          if (scrollTop > windowHeight) {
            setIsVisible(false)
          } else {
            setIsVisible(true)
          }

          // Calculate progress (0 to 1) only within maxScrollHeight
          let scrollPercentage = Math.min(Math.max(scrollTop / maxScrollHeight, 0), 1)

          // Map scroll percentage to video time
          const videoDuration = video.duration || 1
          const targetTime = scrollPercentage * videoDuration

          // Only update if the difference is significant (reduce frequent updates)
          if (Math.abs(video.currentTime - targetTime) > 0.05) {
            video.currentTime = targetTime
          }
          
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener("scroll", handleScroll)

    // Initial call to set the correct position
    setTimeout(handleScroll, 100)

    return () => {
      video.removeEventListener("loadeddata", handleVideoLoaded)
      video.removeEventListener("error", handleLoadError)
      window.removeEventListener("scroll", handleScroll)
    }
  }, [isVideoLoaded])

  return (
    <div
      ref={containerRef}
      className="fixed top-0 left-0 w-full h-screen overflow-hidden transition-opacity duration-300"
      style={{
        opacity: isVisible ? 1 : 0,
        pointerEvents: isVisible ? 'auto' : 'none',
        // Mobile-specific container adjustments
        ...(isMobile && {
          width: '100vw',
          height: '100vh',
          maxWidth: 'none'
        })
      }}
    >
      {!isVideoLoaded && !loadError && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
          <div className="w-12 h-12 border-4 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}

      {loadError && (
        <div className="absolute inset-0 bg-white flex items-center justify-center">
          <div className="text-center p-4">
            <p className="text-red-500 mb-2">Failed to load animation</p>
            <div className="w-full h-full bg-white"></div>
          </div>
        </div>
      )}

      <video
        ref={videoRef}
        src="/Scroll interaction.mp4"
        className="absolute top-0 left-0 w-full h-full object-cover"
        style={{
          opacity: isVideoLoaded ? 1 : 0,
          transition: "opacity 0.5s ease-in-out",
          // Mobile-specific video positioning
          ...(isMobile && {
            objectPosition: 'center center',
            objectFit: 'cover',
            width: '100%',
            height: '100%',
            minHeight: '100vh'
          }),
          // Desktop positioning (preserve existing)
          ...(!isMobile && {
            objectPosition: 'center',
            objectFit: 'cover'
          })
        }}
        muted
        playsInline
        preload="metadata"
      />
    </div>
  )
}
